<!doctype html>
<html
  class="no-js{% if request.design_mode %} shopify-design-mode{% endif %}"
  lang="{{ request.locale.iso_code }}"
>
  <head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@glidejs/glide/dist/css/glide.core.min.css">
    {{ 'base.css' | asset_url | stylesheet_tag }}
    {{ 'custom.css' | asset_url | stylesheet_tag }}
    {%- if settings.favicon != blank -%}
      <link
        rel="icon"
        type="image/png"
        href="{{ settings.favicon | image_url: width: 32, height: 32 }}"
      >
    {%- endif -%}

    {% comment %} This a way to wait for main content to load when navigating to a new page so that the view transitions can work consistently {% endcomment %}
    <link
      rel="expect"
      href="#MainContent"
      blocking="render"
      id="view-transition-render-blocker"
    >
  <script src="https://cdn.jsdelivr.net/npm/@glidejs/glide/dist/glide.min.js"></script>
  <script src="{{ 'custom.js' | asset_url }}" type="module"></script>

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
{% comment %}  <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@100..900&display=swap" rel="stylesheet">       {% endcomment %}
    
    
    {%- render 'meta-tags' -%}
    {%- render 'fonts' -%}
    {%- render 'scripts' -%}
    {%- render 'theme-styles-variables' -%}
    {%- render 'color-schemes' -%}

    {% if request.design_mode %}
      {%- render 'theme-editor' -%}
    {% endif %}

    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    {{ content_for_header }}
  </head>

  <body class="page-width-{{ settings.page_width }} card-hover-effect-{{ settings.card_hover_effect }} {{ template.name }}">
    {% render 'skip-to-content-link', href: '#MainContent', text: 'accessibility.skip_to_text' %}
    <div id="header-group">
      {% sections 'header-group' %}
    </div>

    <main
      id="MainContent"
      class="content-for-layout"
      role="main"
      data-page-transition-enabled="{{ settings.page_transition_enabled }}"
      data-product-transition="{{ settings.transition_to_main_product }}"
      data-template="{{ template }}"
    >
      {{ content_for_layout }}
    </main>

    {% sections 'footer-group' %}

    {% render 'search-modal' %}

    {% # theme-check-disable ParserBlockingScript %}
    <script src="{{ 'critical.js' | asset_url }}"></script>
    {% # theme-check-enable ParserBlockingScript %}

    {% if settings.quick_add or settings.mobile_quick_add %}
      {% render 'quick-add-modal' %}
    {% endif %}
    
  </body>
</html>
{% if template == "product" %}
  <style>
    .header__row.header__row--top.color-scheme-58084d4c-a86e-4d0a-855e-a0966e5043f7.section.section--full-width-margin.section--page-width {
    background: white;
}
@media only screen and (min-width: 1024px) {
    .header__columns.spacing-style {
        background: #fff;
        margin-top: 22px;
        border-radius: 10px;
        box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
        margin-bottom: 35px;
    }
}
  </style>
{% endif %}