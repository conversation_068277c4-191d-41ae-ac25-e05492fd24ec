{% comment %}
  Custom section to display all blog posts from all blogs
{% endcomment %}

<div class="all-blogs-section section color-{{ section.settings.color_scheme }}" 
     style="{% render 'spacing-style', settings: section.settings %}">
  
  {% if section.settings.heading != blank %}
    <div class="all-blogs-header">
      <h2 class="all-blogs-title">{{ section.settings.heading }}</h2>
      {% if section.settings.description != blank %}
        <div class="all-blogs-description">{{ section.settings.description }}</div>
      {% endif %}
    </div>
  {% endif %}

  <div class="all-blogs-container" id="all-blogs-container">
    {% assign all_articles = '' | split: ',' %}
    
    {% comment %} Collect all articles from all blogs {% endcomment %}
    {% for blog in blogs %}
      {% for article in blog.articles limit: section.settings.articles_per_blog %}
        {% assign article_with_blog = article | json | remove: '}' %}
        {% assign article_with_blog = article_with_blog | append: ',"blog_title":"' | append: blog.title | append: '","blog_handle":"' | append: blog.handle | append: '"}' %}
        {% assign all_articles = all_articles | push: article_with_blog %}
      {% endfor %}
    {% endfor %}

    {% comment %} Sort articles by date (newest first) {% endcomment %}
    {% assign sorted_articles = all_articles | sort: 'published_at' | reverse %}

    <div class="all-blogs-grid" style="grid-template-columns: repeat({{ section.settings.columns_desktop }}, 1fr);">
      {% for blog in blogs %}
        {% for article in blog.articles limit: section.settings.articles_per_blog %}
          <article class="all-blogs-card">
            <div class="all-blogs-card-image" style="height: {{ section.settings.image_height }}px;">
              {% if article.image %}
                <a href="{{ article.url }}">
                  <img src="{{ article.image | image_url: width: 600 }}"
                       alt="{{ article.image.alt | escape }}"
                       loading="lazy"
                       width="600"
                       height="{{ section.settings.image_height }}">
                </a>
              {% else %}
                <div class="all-blogs-card-image-placeholder">
                  {{ 'blog-apparel-1' | placeholder_svg_tag }}
                </div>
              {% endif %}
            </div>
            
            <div class="all-blogs-card-content">
              {% if section.settings.show_blog_name %}
                <div class="all-blogs-card-meta">
                  <span class="blog-name">{{ blog.title }}</span>
                  {% if section.settings.show_date %}
                    <span class="blog-date">{{ article.published_at | date: '%d.%m.%Y' }}</span>
                  {% endif %}
                </div>
              {% endif %}

              <h3 class="all-blogs-card-title">
                <a href="{{ article.url }}">{{ article.title }}</a>
              </h3>

              {% if section.settings.show_excerpt and article.excerpt != blank %}
                <div class="all-blogs-card-excerpt">
                  {{ article.excerpt | truncate: section.settings.excerpt_length }}
                </div>
              {% endif %}

              {% if section.settings.show_read_more %}
                <a href="{{ article.url }}" class="all-blogs-card-link">
                  <span class="blog-icon">
                    <img src="https://cdn.shopify.com/s/files/1/0714/5881/6158/files/Frame_1984077742_1.png?v=1750752289"/>
                  </span>
                  {{ section.settings.read_more_text }}
                </a>
              {% endif %}
            </div>
          </article>
        {% endfor %}
      {% endfor %}
    </div>

    {% if section.settings.show_load_more %}
      <div class="all-blogs-load-more">
        <button class="load-more-btn" onclick="loadMoreArticles()">
          {{ section.settings.load_more_text }}
        </button>
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "All Blogs",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "כל הכתבות"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "range",
      "id": "articles_per_blog",
      "label": "Articles per blog",
      "min": 1,
      "max": 50,
      "step": 1,
      "default": 10
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "label": "Desktop columns",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 3
    },
    {
      "type": "range",
      "id": "columns_mobile",
      "label": "Mobile columns",
      "min": 1,
      "max": 2,
      "step": 1,
      "default": 1
    },
    {
      "type": "range",
      "id": "image_height",
      "label": "Image height",
      "min": 200,
      "max": 500,
      "step": 10,
      "unit": "px",
      "default": 300
    },
    {
      "type": "checkbox",
      "id": "show_blog_name",
      "label": "Show blog name",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "label": "Show date",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_excerpt",
      "label": "Show excerpt",
      "default": true
    },
    {
      "type": "range",
      "id": "excerpt_length",
      "label": "Excerpt length",
      "min": 50,
      "max": 300,
      "step": 10,
      "default": 150
    },
    {
      "type": "checkbox",
      "id": "show_read_more",
      "label": "Show read more button",
      "default": true
    },
    {
      "type": "text",
      "id": "read_more_text",
      "label": "Read more text",
      "default": "לקריאה"
    },
    {
      "type": "checkbox",
      "id": "show_load_more",
      "label": "Show load more button",
      "default": false
    },
    {
      "type": "text",
      "id": "load_more_text",
      "label": "Load more text",
      "default": "טען עוד"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "Top padding",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "Bottom padding",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 40
    }
  ],
  "presets": [
    {
      "name": "All Blogs",
      "category": "Blog"
    }
  ]
}
{% endschema %}

<style>
.all-blogs-section {
  --page-content-width: var(--narrow-page-width);
  --page-width: calc(var(--page-content-width) + (var(--page-margin) * 2));
  direction: rtl;
}

.all-blogs-header {
  text-align: center;
  margin-bottom: 2rem;
}

.all-blogs-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: var(--color-foreground);
}

.all-blogs-description {
  font-size: 1.1rem;
  color: var(--color-foreground-75);
  max-width: 600px;
  margin: 0 auto;
}

.all-blogs-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .all-blogs-grid {
    grid-template-columns: repeat(1, 1fr);
    gap: 1.5rem;
  }
}

.all-blogs-card {
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.all-blogs-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.all-blogs-card-image {
  position: relative;
  overflow: hidden;
  height: 300px;
}

.all-blogs-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.all-blogs-card:hover .all-blogs-card-image img {
  transform: scale(1.05);
}

.all-blogs-card-image-placeholder {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.all-blogs-card-content {
  padding: 1.5rem;
}

.all-blogs-card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.blog-name {
  background: #005e43;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-weight: 500;
  font-size: 0.8rem;
}

.blog-date {
  color: #666;
  font-size: 0.85rem;
}

.all-blogs-card-title {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  line-height: 1.4;
}

.all-blogs-card-title a {
  color: #000;
  text-decoration: none;
  transition: color 0.3s ease;
}

.all-blogs-card-title a:hover {
  color: #005e43;
}

.all-blogs-card-excerpt {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.all-blogs-card-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #005e43;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.all-blogs-card-link:hover {
  color: #003d2e;
}

.all-blogs-card-link .blog-icon img {
  width: 16px;
  height: 16px;
}

.all-blogs-load-more {
  text-align: center;
  margin-top: 2rem;
}

.load-more-btn {
  background: #005e43;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.load-more-btn:hover {
  background: #003d2e;
}
</style>
