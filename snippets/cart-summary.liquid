{%- doc -%}
  Renders the cart summary totals.
{%- enddoc -%}

<div class="cart__summary-totals">
  {% # We need to keep this node in place to allow morphing to work properly # %}
  <div class="cart__original-total-container cart-primary-typography">
    {%- if cart.cart_level_discount_applications.size > 0 -%}
      <span class="cart__summary-item cart__original-total">
        <span class="cart__original-total-label">{{ 'content.cart_subtotal' | t }}</span>
        <span class="cart__original-total-value cart-secondary-typography">
          {{- cart.original_total_price | money -}}
        </span>
      </span>
      <div class="cart__summary-discounts">
        <ul
          class="discounts list-unstyled"
          role="list"
          aria-label="{{ 'content.discounts' | t }}"
        >
          {%- for discount in cart.cart_level_discount_applications -%}
            <li class="cart__summary-item cart__discount">
              <span class="cart__discount-label">
                {{- 'icon-discount.svg' | inline_asset_content -}}
                {{ discount.title | escape }}
              </span>
              <span class="cart__discount-value cart-secondary-typography"
                >-{{ discount.total_allocated_amount | money -}}
              </span>
            </li>
          {%- endfor -%}
        </ul>
      </div>
    {%- endif -%}
  </div>
{% comment %}
  {% if settings.show_cart_note or settings.show_add_discount_code %}
    <div class="cart-actions">
      {% if settings.show_cart_note %}
        {% render 'cart-note' %}
      {% endif %}

      {% if settings.show_add_discount_code %}
        {% render 'cart-discount', section_id: section.id %}
      {% endif %}
    </div>
  {% endif %}
  {% endcomment %}
  <div class="cart-discount-form">
  <input required type="text" id="discount-code" placeholder="יש לך קוד קופון? הכניס/י כאן" />
  <button onclick="applyDiscount()">הוסיפו</button>
</div>
<script>
  function applyDiscount() {
    var code = document.getElementById('discount-code').value;
    if (code) {
      window.location.href = '/checkout?discount=' + encodeURIComponent(code);
    }
  }
</script>

  {%- liquid
    if settings.currency_code_enabled_cart_total
      assign total_price = cart.total_price | money_with_currency
    else
      assign total_price = cart.total_price | money
    endif
  -%}
  
  <div class="tax-total">
     <span
      class="cart__summary-item cart__total"
      role="status"
    >
      <span class="cart__total-label cart-primary-typography tax-heading">סכום ביניים </span>
      <text-component
        ref="cartTotal"
        value="{{ total_price | strip_html }}"
        class="cart__total-value cart-secondary-typography first-price"
        {% comment %} Used by payment_terms web component {% endcomment %}
        data-cart-subtotal
      >
        {{ total_price }}
      </text-component>
    </span>
    
  </div>
  
    <div class="tax-text">
     <span
      class="cart__summary-item cart__total"
      role="status"
    >
      <span class="cart__total-label cart-primary-typography local-tax">משלוח</span>
      <text-component
        ref="cartTotal"
        value="{{ total_price | strip_html }}"
        class="cart__total-value cart-secondary-typography sab-tax-local"
        {% comment %} Used by payment_terms web component {% endcomment %}
        data-cart-subtotal
      >
        יחושב בשלב הבא
      </text-component>
    </span>
    
  </div>
  
  <div class="cart__total-container">
    <span
      class="cart__summary-item cart__total"
      role="status"
    >
      <span class="cart__total-label cart-primary-typography estimate-text">{{ 'content.cart_estimated_total' | t }}</span>
      <text-component
        ref="cartTotal"
        value="{{ total_price | strip_html }}"
        class="cart__total-value cart-secondary-typography estimate-price"
        {% comment %} Used by payment_terms web component {% endcomment %}
        data-cart-subtotal
      >
        {{ total_price }}
      </text-component>
    </span>
    {% if settings.show_installments %}
      <span class="cart__summary-item cart__installments">
        {% form 'cart', cart %}
          {{ form | payment_terms }}
        {% endform %}
      </span>
    {% endif %}
    <div class="cart__summary-item tax-note cart-primary-typography">
      {% render 'tax-info', has_discounts_enabled: settings.show_add_discount_code %}
    </div>
  </div>
</div>

<div class="cart__ctas">
  <button
    type="submit"
    id="checkout"
    class="cart__checkout-button button"
    name="checkout"
    {% if cart == empty %}
      disabled
    {% endif %}
    form="cart-form"
  >
    {{ 'content.checkout' | t }}  <span class="svg-wrapper add-to-cart-icon">
        <img src="https://cdn.shopify.com/s/files/1/0714/5881/6158/files/Vector_3.svg?v=1753014930">
        </span>
  </button>

  {% if additional_checkout_buttons and settings.show_accelerated_checkout_buttons %}
    <div class="additional-checkout-buttons additional-checkout-buttons--vertical">
      {{ content_for_additional_checkout_buttons }}
    </div>
  {% endif %}

  <div class="total-pricee">
   <h2 class="btn-withtotal" style="font-size: 24px;font-weight: bold;"> סה״כ {{ total_price }} 
   </h2>
   </div>
  
</div>

{% stylesheet %}
  .cart-actions {
    display: flex;
    flex-direction: column;
    gap: var(--gap-2xs);
    border-block: 1px solid var(--color-border);
    padding-block: var(--padding-sm);
    margin-block-start: var(--margin-3xs);
  }

  .cart__summary-totals:not(:has(.cart-actions)) {
    margin-block-start: var(--margin-3xs);
    border-block-start: 1px solid var(--color-border);
    padding-block-start: var(--margin-xl);
  }

  .cart__installments {
    color: var(--color-foreground);
  }
  .cart__ctas {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
}
  .total-pricee {
    flex: 0 0 55%;
}
  button#checkout {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    font-weight: 500;
    border-radius: 8px;
    background: #007A73;
}
  .tax-heading {
    font-size: 16px;
    font-weight: 500;
    color: #323438;
}
  .first-price{
     font-size: 16px;
    font-weight: 500;
    color: #323438;
  }
  .local-tax{
     font-size: 16px;
    font-weight: 500;
    color: #323438;
  }
  .sab-tax-local{
    color: #A5A8AD;
     font-size: 16px;
    font-weight: 500;
  }
  .estimate-text{
    font-size: 16px;
    font-weight: bold;
  }
   .estimate-price{
    font-size: 16px;
    font-weight: bold;
  }
  
{% endstylesheet %}
